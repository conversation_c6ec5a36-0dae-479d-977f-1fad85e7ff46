<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>info.znet.zcloud</groupId>
    <artifactId>z-cloud</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <modules>
        <module>z-common</module>
        <module>biz-center</module>
        <module>push-service</module>
        <module>cloud-monitor</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.12</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <project.version>1.0.0</project.version>
        <skipTests>true</skipTests>
        <docker.host>http://*************:2375</docker.host>
        <docker.maven.plugin.version>0.43.3</docker.maven.plugin.version>
        <java.version>17</java.version>
        <spring-boot.version>3.2.12</spring-boot.version>
        <spring-cloud.version>2023.0.6</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.3.3</spring-cloud-alibaba.version>
        <mysql-connector.version>8.3.0</mysql-connector.version>
        <redisson.version>3.50.0</redisson.version>
        <druid.version>1.2.27</druid.version>
        <mybatis-flex-starter.version>1.11.1</mybatis-flex-starter.version>
        <seata-starter.version>2.5.0</seata-starter.version>
        <dubbo-starter.version>3.3.5</dubbo-starter.version>
        <hutool.version>5.8.39</hutool.version>
        <spring-boot-admin.version>3.2.3</spring-boot-admin.version>

        <spring-data-commons.version>3.2.2</spring-data-commons.version>
        <aliyun-oss.version>3.16.0</aliyun-oss.version>
        <logstash-logback.version>5.3</logstash-logback.version>
        <minio.version>8.4.5</minio.version>
        <sa-token.version>1.37.0</sa-token.version>

        <z-common.version>1.0.0</z-common.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!--Spring Cloud 相关依赖-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--Spring Cloud Alibaba 相关依赖-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${seata-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-nacos-spring-boot-starter</artifactId>
                <version>${dubbo-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-sentinel-spring-boot-starter</artifactId>
                <version>${dubbo-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-seata-spring-boot-starter</artifactId>
                <version>${dubbo-starter.version}</version>
            </dependency>

            <!--common 通用模块-->
            <dependency>
                <groupId>info.znet.zcloud</groupId>
                <artifactId>z-common</artifactId>
                <version>${z-common.version}</version>
            </dependency>
            <!--Mysql数据库驱动-->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>
            <!--druid连接池-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-spring-boot3-starter</artifactId>
                <version>${mybatis-flex-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-codegen</artifactId>
                <version>${mybatis-flex-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <!--Hutool Java工具包-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!--SpringData工具包-->
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-commons</artifactId>
                <version>${spring-data-commons.version}</version>
            </dependency>

            <!-- 阿里云OSS -->
            <!--            <dependency>-->
            <!--                <groupId>com.aliyun.oss</groupId>-->
            <!--                <artifactId>aliyun-sdk-oss</artifactId>-->
            <!--                <version>${aliyun-oss.version}</version>-->
            <!--            </dependency>-->

            <!--集成logstash-->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback.version}</version>
            </dependency>
            <!--集成SpringBoot Admin监控-->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <!--集成SpringBoot Admin监控客户端-->
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证（Reactor响应式集成） -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-reactor-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <!-- Sa-Token 权限认证 -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <!-- Sa-Token 整合 Redis (使用jackson序列化方式) -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-redis-jackson</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <!-- Sa-Token 整合 jwt -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>io.fabric8</groupId>
                    <artifactId>docker-maven-plugin</artifactId>
                    <version>${docker.maven.plugin.version}</version>
                    <executions>
                        <!--如果想在项目打包时构建镜像添加-->
                        <execution>
                            <id>build-image</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <!-- Docker 远程管理地址-->
                        <dockerHost>${docker.host}</dockerHost>
                        <images>
                            <image>
                                <!--定义镜像名称-->
                                <name>mall/${project.name}:${project.version}</name>
                                <!--定义镜像构建行为-->
                                <build>
                                    <!--定义基础镜像-->
                                    <from>openjdk:17</from>
                                    <args>
                                        <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                                    </args>
                                    <!--定义哪些文件拷贝到容器中-->
                                    <assembly>
                                        <!--定义拷贝到容器的目录-->
                                        <targetDir>/</targetDir>
                                        <!--只拷贝生成的jar包-->
                                        <descriptorRef>artifact</descriptorRef>
                                    </assembly>
                                    <!--定义容器启动命令-->
                                    <entryPoint>["java",
                                        "-jar","-Dspring.profiles.active=prod","/${project.build.finalName}.jar"]
                                    </entryPoint>
                                    <!--定义维护者-->
                                    <maintainer>tamia</maintainer>
                                </build>
                            </image>
                        </images>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <!--解决SpringBoot 3.2 Parameter Name Retention 问题-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <!--使用aliyun的镜像源提升依赖下载速度-->
    <repositories>
        <repository>
            <id>aliyunmaven</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
        <repository>
            <id>central2</id>
            <name>central2</name>
            <url>https://repo1.maven.org/maven2/</url>
        </repository>
    </repositories>

    <!--使用aliyun的镜像源提升插件下载速度-->
    <pluginRepositories>
        <pluginRepository>
            <id>aliyunmaven</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </pluginRepository>
        <pluginRepository>
            <id>central2</id>
            <name>central2</name>
            <url>https://repo1.maven.org/maven2/</url>
        </pluginRepository>
    </pluginRepositories>

</project>