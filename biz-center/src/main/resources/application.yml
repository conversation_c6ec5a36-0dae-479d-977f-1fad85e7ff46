server:
  port: 8201
spring:
  application:
    name: biz-center
  banner:
    location: banner
  profiles:
    active: dev
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB
      max-request-size: 200MB
  messages:
    basename: i18n.messages
    encoding: UTF-8
  #  config:
  #    import: "nacos:"
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: *****************************************************************************************************
    username: root
    password: root
    druid:
      initial-size: 5 #连接池初始化大小
      min-idle: 10 #最小空闲连接数
      max-active: 20 #最大连接数
      web-stat-filter:
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*" #不统计这些请求数据
      stat-view-servlet: #访问监控网页的登录用户名和密码
        login-username: druid
        login-password: druid
  data:
    redis:
      host: localhost # Redis服务器地址
      database: 0 # Redis数据库索引（默认为0）
      port: 6379 # Redis服务器连接端口
      #      password:  # Redis服务器连接密码（默认为空）
      timeout: 3000ms # 连接超时时间（毫秒）
dubbo:
  application:
    name: biz-dubbo
  protocol:
    name: tri
    port: 58102
  registry:
    address: nacos://${nacos.address:127.0.0.1}:8848?group=dubbo-group
  provider:
    timeout: 5000
management:
  endpoints:
    web:
      exposure:
        include: '*' #暴露Actuator的监控端点
  endpoint:
    health:
      show-details: always #显示健康的详细信息
    env:
      show-values: always #默认Actuator的环境变量会以****显示，这里开启显示
    configprops:
      show-values: always #默认Actuator的配置属性会以****显示，这里开启显示
    info:
      enabled: true

