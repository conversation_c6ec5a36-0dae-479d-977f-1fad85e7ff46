package info.znet.zcloud.config;

import com.mybatisflex.annotation.InsertListener;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.UpdateListener;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.core.audit.ConsoleMessageCollector;
import com.mybatisflex.core.audit.MessageCollector;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.mybatisflex.core.logicdelete.LogicDeleteManager;
import com.mybatisflex.core.logicdelete.impl.DateTimeLogicDeleteProcessor;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 */

@Configuration
@EnableTransactionManagement
@MapperScan({"generator.mapper"})
@ComponentScan(basePackages = {"info.znet.zcloud", "generator.service"})
public class MyBatisFlexConfig implements MyBatisFlexCustomizer {

    @Value("${spring.profiles.active}")
    private String activeProfile;


    @Override
    public void customize(FlexGlobalConfig flexGlobalConfig) {
        if (!"prod".equals(activeProfile)) {
            // 开启审计功能
            AuditManager.setAuditEnable(true);

            // 设置 SQL 审计收集器
            MessageCollector collector = new ConsoleMessageCollector();
            AuditManager.setMessageCollector(collector);
        }
        // 配置主键生成策略
        FlexGlobalConfig.KeyConfig keyConfig = new FlexGlobalConfig.KeyConfig();
        keyConfig.setKeyType(KeyType.Generator);
        keyConfig.setValue(KeyGenerators.snowFlakeId);
        keyConfig.setBefore(true);
        flexGlobalConfig.setKeyConfig(keyConfig);

        flexGlobalConfig.setLogicDeleteColumn("delete_time");
        LogicDeleteManager.setProcessor(new DateTimeLogicDeleteProcessor());

        // 插入时自动填充
        flexGlobalConfig.registerInsertListener(new InsertListener() {
            @Override
            public void onInsert(Object o) {
                Map<String, Object> data = (Map<String, Object>) o;
                data.put("createTime", LocalDateTime.now());
                data.put("updateTime", LocalDateTime.now());
            }
        });

        // 更新时自动填充
        flexGlobalConfig.registerUpdateListener(new UpdateListener() {
            @Override
            public void onUpdate(Object o) {
                
            }
        });
    }
}