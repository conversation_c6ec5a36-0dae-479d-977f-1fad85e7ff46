package info.znet.zcloud.controller;


//import info.znet.zcloud.response.JsonResult;

import info.znet.zcloud.consumer.PushConsumer;
import info.znet.zcloud.dubbo.dto.push.req.DemoHelloReqDto;
import info.znet.zcloud.dubbo.dto.push.req.MessageSendEmailReqDto;
import info.znet.zcloud.dubbo.dto.push.res.DemoHelloResDto;
import info.znet.zcloud.dubbo.service.push.MessageService;
import info.znet.zcloud.response.JsonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController()
@RequestMapping("/example")
public class ExampleController {


    @Autowired
    private PushConsumer pushConsumer;


    /**
     * 数据库测试示例
     */
    @GetMapping("/db")
    @ResponseBody
    public JsonResult<Object> db() {

        return JsonResult.ok();
    }


    // 也可以直接引入
    @DubboReference
    private MessageService messageService;

    /**
     * RPC测试示例
     */
    @GetMapping("/rpc")
    @ResponseBody
    public JsonResult<Object> dubbo() {
        DemoHelloResDto res = pushConsumer.hello(DemoHelloReqDto.builder().who("tim").msg("success").build());
        log.info("RPC 调用成功: Received response: {}", res);

        Boolean sendEmail = pushConsumer.sendEmail("<EMAIL>", "123456");
        log.info("sendEmail response: {}", sendEmail);

        Boolean sendEmail2 = messageService.sendEmail(MessageSendEmailReqDto.builder()
                .sendTo("<EMAIL>")
                .content("123456")
                .build());
        log.info("sendEmail2 response: {}", sendEmail2);

        return JsonResult.ok();
    }


}
