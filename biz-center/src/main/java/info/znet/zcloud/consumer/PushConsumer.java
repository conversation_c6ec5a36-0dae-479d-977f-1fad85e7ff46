package info.znet.zcloud.consumer;

import info.znet.zcloud.dubbo.dto.push.req.DemoHelloReqDto;
import info.znet.zcloud.dubbo.dto.push.req.MessageSendEmailReqDto;
import info.znet.zcloud.dubbo.dto.push.res.DemoHelloResDto;
import info.znet.zcloud.dubbo.service.push.DemoService;
import info.znet.zcloud.dubbo.service.push.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class PushConsumer implements CommandLineRunner {

    @DubboReference
    private DemoService demoService;

    @DubboReference
    private MessageService messageService;

    @Override
    public void run(String... args) throws Exception {

    }

    public DemoHelloResDto hello(DemoHelloReqDto dto) {
        return demoService.sayHello(dto);
    }

    public Boolean sendEmail(String to, String text, String subject) {
        return messageService.sendEmail(MessageSendEmailReqDto.builder()
                .sendTo(to)
                .subject(subject)
                .content(text)
                .build());
    }

    public Boolean sendEmail(String to, String text) {
        return messageService.sendEmail(MessageSendEmailReqDto.builder()
                .sendTo(to)
                .content(text)
                .build());
    }
}
