package generator.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

import java.io.Serial;

/**
 * 商户钱包 表定义层。
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
public class WalletTableDef extends TableDef {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 商户钱包
     */
    public static final WalletTableDef WALLET = new WalletTableDef();

    
    public final QueryColumn ID = new QueryColumn(this, "id");

    /**
     * 1USDT 2 USDC
11USD  
     */
    public final QueryColumn TYPE = new QueryColumn(this, "type");

    /**
     * 商户 ID
     */
    public final QueryColumn BIZ_ID = new QueryColumn(this, "biz_id");

    
    public final QueryColumn FROZEN = new QueryColumn(this, "frozen");

    
    public final QueryColumn BALANCE = new QueryColumn(this, "balance");

    
    public final QueryColumn DECIMAL = new QueryColumn(this, "decimal");

    
    public final QueryColumn SIGNATURE = new QueryColumn(this, "signature");

    
    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    
    public final QueryColumn UPDATE_TIME = new QueryColumn(this, "update_time");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, TYPE, BIZ_ID, DECIMAL, BALANCE, FROZEN, SIGNATURE, CREATE_TIME, UPDATE_TIME};

    public WalletTableDef() {
        super("", "wallet");
    }

    private WalletTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public WalletTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new WalletTableDef("", "wallet", alias));
    }

}
