package generator.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Timestamp;

/**
 * 商户钱包 实体类。
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table("wallet")
public class Wallet implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    private BigInteger id;

    /**
     * 1USDT 2 USDC 11USD
     */
    private Integer type;

    /**
     * 商户 ID
     */
    private BigInteger bizId;

    private Integer decimal;

    private String balance;

    private String frozen;

    private String signature;

    private Timestamp createTime;

    private Timestamp updateTime;

}
