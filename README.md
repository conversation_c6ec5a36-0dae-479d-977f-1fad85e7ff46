# z-cloud

## 服务定义

| 服务名              | 描述                     | 端口   |
|------------------|------------------------|------|
| gateway-service  | 服务网关                   | 8000 |
| ---              |                        |      |
| admin-service    | 运营端                    | 8101 |
| console-service  | 商户端                    | 8102 |
| api-service      | openapi                | 8103 |
| sdk-service      | sdk                    | 8104 | 
| ---              |                        |      | 
| biz-center       | 商户中心                   | 8201 |
| card-service     | 卡台                     | 8202 |
| pay4o-service    | 代付                     | 8203 |
| exchange-service | 汇兑                     | 8204 |
| upay-service     | 收单                     | 8205 |
| token-service    | 数币                     | 8206 |
|                  |                        |      |
| push-service     | 推送服务                   | 8301 |
| audit-service    | 审计服务                   | 8302 |
| ---              |                        |      |
| cloud-monitor    | spring-boot-admin 服务监控 | 8401 |

## 基础设施

| 服务名           | 描述          | 端口   |
|---------------|-------------|------|
| nacos         | 注册中心        | 8101 |
| cloud-monitor | spring 服务监控 | 8101 |

## 技术栈

| 组件                   | 说明        | 文档                                                   |
|----------------------|-----------|------------------------------------------------------|
| Spring Boot          | 基础网络框架    | https://spring.io/projects/spring-boot               |
| Spring Cloud         | 微服务框架     | https://spring.io/projects/spring-cloud              |
| Spring Cloud Alibaba | 微服务框架     | https://github.com/alibaba/spring-cloud-alibaba      |
| Nacos                | 注册配置中心    | https://nacos.io/                                    |
| Dubbo                | RPC框架     | https://cn.dubbo.apache.org/                         |
| Seata                | 分布式事务     | https://github.com/seata/seata                       |
| Druid                | 数据库连接池    | https://github.com/alibaba/druid                     |
| MyBatis-Flex         | ORM框架     | https://mybatis-flex.com                             |
| Redisson             | 分布式缓存     | https://redisson.pro/docs                            |
| MongoDb              | NoSql数据库  | https://www.mongodb.com/                             |
| Sa-Token             | 认证和授权     | https://github.com/dromara/Sa-Token                  |
| OSS                  | 对象存储      | https://github.com/aliyun/aliyun-oss-java-sdk        |
| LogStash             | 日志收集      | https://github.com/logstash/logstash-logback-encoder |
| Docker               | 应用容器引擎    | https://www.docker.com/                              |
| Portainer            | Docker可视化 | https://github.com/portainer/portainer               |
| Jenkins              | 自动化部署工具   | https://github.com/jenkinsci/jenkins                 |
| Kubernetes           | 应用容器管理平台  | https://kubernetes.io/                               |