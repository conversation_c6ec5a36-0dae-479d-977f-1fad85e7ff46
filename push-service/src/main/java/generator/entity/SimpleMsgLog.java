package generator.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Timestamp;

import java.io.Serial;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 *  实体类。
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table("simple_msg_log")
public class SimpleMsgLog implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    private BigInteger id;

    private String caller;

    /**
     * 1 email 2 phone 
     */
    private Integer channel;

    private String sendTo;

    private String subject;

    private String content;

    private Boolean status;

    private Timestamp createTime;

}
