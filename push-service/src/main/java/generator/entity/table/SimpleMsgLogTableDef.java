package generator.entity.table;

import com.mybatisflex.core.query.QueryColumn;
import com.mybatisflex.core.table.TableDef;

import java.io.Serial;

/**
 *  表定义层。
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
public class SimpleMsgLogTableDef extends TableDef {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    public static final SimpleMsgLogTableDef SIMPLE_MSG_LOG = new SimpleMsgLogTableDef();

    
    public final QueryColumn ID = new QueryColumn(this, "id");

    
    public final QueryColumn CALLER = new QueryColumn(this, "caller");

    
    public final QueryColumn SEND_TO = new QueryColumn(this, "send_to");

    
    public final QueryColumn STATUS = new QueryColumn(this, "status");

    /**
     * 1 email 2 phone 
     */
    public final QueryColumn CHANNEL = new QueryColumn(this, "channel");

    
    public final QueryColumn CONTENT = new QueryColumn(this, "content");

    
    public final QueryColumn SUBJECT = new QueryColumn(this, "subject");

    
    public final QueryColumn CREATE_TIME = new QueryColumn(this, "create_time");

    /**
     * 所有字段。
     */
    public final QueryColumn ALL_COLUMNS = new QueryColumn(this, "*");

    /**
     * 默认字段，不包含逻辑删除或者 large 等字段。
     */
    public final QueryColumn[] DEFAULT_COLUMNS = new QueryColumn[]{ID, CALLER, CHANNEL, SEND_TO, SUBJECT, CONTENT, STATUS, CREATE_TIME};

    public SimpleMsgLogTableDef() {
        super("", "simple_msg_log");
    }

    private SimpleMsgLogTableDef(String schema, String name, String alisa) {
        super(schema, name, alisa);
    }

    public SimpleMsgLogTableDef as(String alias) {
        String key = getNameWithSchema() + "." + alias;
        return getCache(key, k -> new SimpleMsgLogTableDef("", "simple_msg_log", alias));
    }

}
