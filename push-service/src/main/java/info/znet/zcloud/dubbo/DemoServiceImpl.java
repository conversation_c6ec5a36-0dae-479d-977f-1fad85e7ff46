package info.znet.zcloud.dubbo;

import info.znet.zcloud.dubbo.dto.push.req.DemoHelloReqDto;
import info.znet.zcloud.dubbo.dto.push.res.DemoHelloResDto;
import info.znet.zcloud.dubbo.service.push.DemoService;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 */
@DubboService
public class DemoServiceImpl implements DemoService {


    @Override
    public DemoHelloResDto sayHello(DemoHelloReqDto dto) {
        String content = String.format("Hello, %s! You Say %s", dto.getWho(), dto.getMsg());
        return DemoHelloResDto.builder().content(content).build();
    }
}
