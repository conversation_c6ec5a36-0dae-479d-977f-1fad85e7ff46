package info.znet.zcloud.dubbo;

import generator.entity.SimpleMsgLog;
import generator.service.SimpleMsgLogService;
import info.znet.zcloud.dubbo.dto.push.req.MessageSendEmailReqDto;
import info.znet.zcloud.dubbo.service.push.MessageService;
import info.znet.zcloud.service.EmailService;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@DubboService
public class MessageServiceImpl implements MessageService {

    @Value("${sms.test}")
    private Boolean smsTest;

    String caller = RpcContext.getContext().getRemoteApplicationName();

    @Autowired
    private EmailService emailService;

    @Autowired
    SimpleMsgLogService simpleMsgLogService;

    @Override
    public Boolean sendEmail(MessageSendEmailReqDto dto) {
        if (smsTest) {
            return true;
        }
        SimpleMsgLog log = SimpleMsgLog.builder()
                .caller(caller)
                .channel(1)
                .sendTo(dto.getSendTo())
                .subject(dto.getSubject())
                .content(dto.getContent())
                .build();
        Boolean res = emailService.sendSimpleMessage(dto.getSendTo(), dto.getSubject(), dto.getContent());
        log.setStatus(res);
        simpleMsgLogService.save(log);
        return res;
    }
}
