server:
  port: 8301
spring:
  application:
    name: push-service
  banner:
    location: banner
  profiles:
    active: dev
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB
      max-request-size: 200MB
  messages:
    basename: i18n.messages
    encoding: UTF-8
  mail:
    host: smtpout.secureserver.net
    port: 465
    username: <EMAIL>
    password: qwerasdf1234@
    properties:
      mail:
        smtp:
          auth: true
          timeout: 3000
          ssl:
            enable: true

dubbo:
  application:
    name: push-dubbo
    logger: slf4j
  protocol:
    name: tri
    port: 58301
  registry:
    address: nacos://${nacos.address:127.0.0.1}:8848?group=dubbo-group

management:
  endpoints:
    web:
      exposure:
        include: '*' #暴露Actuator的监控端点
  endpoint:
    health:
      show-details: always #显示健康的详细信息
    env:
      show-values: always #默认Actuator的环境变量会以****显示，这里开启显示
    configprops:
      show-values: always #默认Actuator的配置属性会以****显示，这里开启显示
    info:
      enabled: true
