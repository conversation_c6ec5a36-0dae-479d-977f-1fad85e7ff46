spring:
  banner:
    location: banner
  profiles:
    active: dev
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB
      max-request-size: 200MB
  messages:
    basename: i18n.messages
    encoding: UTF-8
  boot:
    admin:
      client:
        url: http://localhost:8080
management:
  endpoints:
    web:
      exposure:
        include: '*' #暴露Actuator的监控端点
  endpoint:
    health:
      show-details: always #显示健康的详细信息
    env:
      show-values: always #默认Actuator的环境变量会以****显示，这里开启显示
    configprops:
      show-values: always #默认Actuator的配置属性会以****显示，这里开启显示
    info:
      enabled: true
