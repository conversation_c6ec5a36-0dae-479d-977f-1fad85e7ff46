package info.znet.zcloud;

import com.alibaba.druid.pool.DruidDataSource;
import com.mybatisflex.codegen.Generator;
import com.mybatisflex.codegen.config.GlobalConfig;

/**
 * <AUTHOR>
 */
public class FlexGenerator {
    private static final String PROJECT_DIR = System.getProperty("user.dir");

    public static void main(String[] args) {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setUrl("**********************************************************************************");
        dataSource.setUsername("root");
        dataSource.setPassword("root");
        GlobalConfig globalConfig = createGlobalConfigUseStyle("biz-center");
        Generator generator = new Generator(dataSource, globalConfig);
        generator.generate();
    }

    public static GlobalConfig createGlobalConfigUseStyle(String serviceName) {
        GlobalConfig globalConfig = new GlobalConfig();
        globalConfig.getPackageConfig()
                .setSourceDir(String.format("%s/%s/src/main/java", PROJECT_DIR, serviceName))
                .setMapperXmlPath(String.format("%s/%s/src/main/resources/mapper", PROJECT_DIR, serviceName))
                .setBasePackage("generator");

        globalConfig.getStrategyConfig()
                .setTablePrefix("")
                .setLogicDeleteColumn("delete_time");
//                .setGenerateTable()
//                .setColumnConfig(ColumnConfig.create().
//                        setColumnName("id")
//                        .setKeyType(KeyType.Generator)
//                        .setKeyValue("KeyGenerators.flexId")
//                );

        globalConfig.enableEntity()
                .setWithLombok(true)
                .setJdkVersion(17)
                .setOverwriteEnable(true);

        globalConfig.enableMapper()
                .setOverwriteEnable(true);

        globalConfig.enableTableDef()
                .setOverwriteEnable(true);

        globalConfig.enableMapperXml()
                .setOverwriteEnable(true);

        globalConfig.enableService();
        globalConfig.enableServiceImpl();

        globalConfig.disableController();
        return globalConfig;
    }
}