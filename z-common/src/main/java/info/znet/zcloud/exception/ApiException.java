package info.znet.zcloud.exception;


import info.znet.zcloud.response.IErrorCode;

/**
 * 自定义API异常
 *
 * <AUTHOR>
 * @updateTime 2024/5/31 10:33
 */
public class ApiException extends RuntimeException {
    private IErrorCode errorCode;

    public ApiException(IErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public IErrorCode getErrorCode() {
        return errorCode;
    }
}
