package info.znet.zcloud.exception;

import info.znet.zcloud.response.IErrorCode;
import lombok.Getter;


/**
 * 自定义API异常
 *
 * <AUTHOR>
 * @updateTime 2024/5/31 10:33
 */
@Getter
public class ParamErrException extends RuntimeException {
    private IErrorCode errorCode;

    public ParamErrException(IErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

}
