package info.znet.zcloud.dubbo.dto.biz.req;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class UserRegisterReqDto implements Serializable {

    @NotBlank
    @Email(message = "{Valid.email}")
    private String email;

    @NotBlank
    @Size(min = 6, max = 20, message = "{Valid.password.Size}")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).*", message = "{Valid.password.Pattern}")
    private String password;

    @NotBlank
    @Length(min = 6, max = 6, message = "{Valid.code}")
    private String captcha;


}
