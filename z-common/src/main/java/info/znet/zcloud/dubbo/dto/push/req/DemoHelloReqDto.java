package info.znet.zcloud.dubbo.dto.push.req;

import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class DemoHelloReqDto implements Serializable {
    private static final long serialVersionUID = 1L;

//    @Email(message = "{Valid.email}")
//    private String email;
//
//
//    @Size(min = 6, max = 20, message = "{Valid.password.Size}")
//    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d).*", message = "{Valid.password.Pattern}")
//    private String password;
//
//
//    @Length(min = 6, max = 6, message = "{Valid.code}")
//    private String code;

    @NotBlank
    private String who;

    @NotBlank
    private String msg;

}
