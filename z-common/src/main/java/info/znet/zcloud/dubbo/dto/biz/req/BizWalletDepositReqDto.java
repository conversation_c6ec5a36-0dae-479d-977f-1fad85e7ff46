package info.znet.zcloud.dubbo.dto.biz.req;

import info.znet.zcloud.enums.WalletTypeEnum;
import info.znet.zcloud.validator.InEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@Data
public class BizWalletDepositReqDto implements Serializable {

    @NotBlank
    private BigInteger bizId;

    @InEnum(value = WalletTypeEnum.class)
    private WalletTypeEnum type;


    private BigDecimal amount;

}
