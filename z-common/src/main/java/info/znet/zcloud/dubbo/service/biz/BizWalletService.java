package info.znet.zcloud.dubbo.service.biz;

import info.znet.zcloud.dubbo.dto.biz.req.BizWalletDepositReqDto;
import info.znet.zcloud.dubbo.dto.biz.req.BizWalletRegisterReqDto;
import info.znet.zcloud.dubbo.dto.biz.res.BizWalletDetailResDto;
import info.znet.zcloud.enums.WalletTypeEnum;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public interface BizWalletService {


    /**
     * 商户钱包注册
     */
    Boolean register(BizWalletRegisterReqDto dto);


    /**
     * 商户钱包信息
     */
    BizWalletDetailResDto detail(BigInteger bizId, WalletTypeEnum type);

    /**
     * 商户钱包存款
     */
    Boolean deposit(BizWalletDepositReqDto dto);

    /**
     * 商户钱包出款
     */
    Boolean withdraw(BizWalletDepositReqDto dto);
}
