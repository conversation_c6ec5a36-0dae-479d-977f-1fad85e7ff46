package info.znet.zcloud.dubbo.dto.biz.req;

import info.znet.zcloud.enums.WalletTypeEnum;
import info.znet.zcloud.validator.InEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@Data
public class BizWalletRegisterReqDto implements Serializable {

    @NotBlank
    private BigInteger bizId;

    @InEnum(value = WalletTypeEnum.class)
    private WalletTypeEnum type;


}
