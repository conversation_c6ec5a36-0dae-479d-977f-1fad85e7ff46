package info.znet.zcloud.dubbo.dto.push.req;

import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Builder
public class MessageSendEmailReqDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;


    @NotBlank
    private String subject = "ZNetwork verification code";

    @NotBlank
    private String sendTo;

    @NotBlank
    private String content;

}
