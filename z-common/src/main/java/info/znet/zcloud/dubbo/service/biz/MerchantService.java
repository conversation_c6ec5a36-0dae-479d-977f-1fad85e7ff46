package info.znet.zcloud.dubbo.service.biz;

import info.znet.zcloud.dubbo.dto.biz.req.UserRegisterReqDto;

/**
 * <AUTHOR>
 */
public interface MerchantService {


    // 商户
    Boolean register(UserRegisterReqDto reqDto);

    /**
     * 获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    String getUserInfo(String userId);

    /**
     * 更新用户信息
     *
     * @param userId   用户ID
     * @param userInfo 用户信息
     * @return 更新结果
     */
    boolean updateUserInfo(String userId, String userInfo);
}
