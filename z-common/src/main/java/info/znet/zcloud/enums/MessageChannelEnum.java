package info.znet.zcloud.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.mybatisflex.annotation.EnumValue;
import info.znet.zcloud.validator.EnumValid;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum MessageChannelEnum implements EnumValid {

    EMAIL("email", "电子邮件"),
    PHONE("phone", "手机短信"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String desc;


    /**
     * 构造方法
     *
     * @param code 编码
     * @param desc 描述
     */
    private MessageChannelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MessageChannelEnum fromCode(String code) {
        for (MessageChannelEnum cardModel : MessageChannelEnum.values()) {
            if (cardModel.code.equals(code)) {
                return cardModel;
            }
        }
        throw new IllegalArgumentException("Invalid SmsChannelEnum code: " + code);
    }
}
