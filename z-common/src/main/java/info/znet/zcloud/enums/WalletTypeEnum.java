package info.znet.zcloud.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.mybatisflex.annotation.EnumValue;
import info.znet.zcloud.validator.EnumValid;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
public enum WalletTypeEnum implements EnumValid {

    USDT("11", "电子邮件"),
    USDC("12", "手机短信"),
    ;

    @EnumValue
    @JsonValue
    private final String code;

    private final String desc;


    /**
     * 构造方法
     *
     * @param code 编码
     * @param desc 描述
     */
    private WalletTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WalletTypeEnum fromCode(String code) {
        for (WalletTypeEnum cardModel : WalletTypeEnum.values()) {
            if (cardModel.code.equals(code)) {
                return cardModel;
            }
        }
        throw new IllegalArgumentException("Invalid WalletTypeEnum code: " + code);
    }
}
