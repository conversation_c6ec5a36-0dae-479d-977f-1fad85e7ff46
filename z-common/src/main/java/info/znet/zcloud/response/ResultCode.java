package info.znet.zcloud.response;


import info.znet.zcloud.utils.SpringContextUtil;
import lombok.Getter;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;


/**
 * 枚举了一些常用API操作码
 *
 * <AUTHOR>
 * @updateTime 2024/5/31 10:21
 */
@Getter
public enum ResultCode implements IErrorCode {

    /**
     * 操作码
     */
    SUCCESS(200, "操作成功"),
    VALIDATE_FAILED(400, "参数检验失败"),
    UNAUTHORIZED(401, "未登录"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FIND(404, "资源不存在"),
    SERVER_ERR(500, "服务器异常"),
    FAILED(9000, "操作失败"),
    UP_ERR(9988, "上游处理失败"),
    SYS_ERR(9999, "系统异常"),


    /**
     * 业务码
     * 1xxx 用户相关
     * 2xxx 卡片相关
     * 3xxx 支付相关
     * 4xxx
     * 9xxx 通用
     */
    USER_NOT_EXIST(1100, "用户不存在"),
    USER_EXIST(1101, "用户已存在"),
    USER_PASSWORD_ERROR(1102, "密码错误"),
    USER_LOCKED(1103, "用户已被锁定"),
    USER_DISABLED(1104, "用户已被禁用"),

    BUSINESS_LOCK(1201, "商户已被锁定"),
    BUSINESS_DISABLED(1202, "商户已被禁用"),

    BUSINESS_RSA_ERR(1301, "商户密钥对错误"),
    BIZ_CODE_EXP(1302, "CODE 已过期"),

    BUSINESS_FEE_LIMIT(1401, "商户费率不能低于平台费率"),

    /**
     * 9xxx 通用
     */
    VCODE_ERR(9001, "验证码错误"),
    REFRESH_EXPIRE(9002, "刷新令牌已过期"),
    IMAGE_ERROR(9003, "certificateImage 所有图片请以.jpg结尾"),
    KYC_ERROR(9004, "KYC用户信息不存在"),

    ENCRYPT_EXP(9100, "加密串已过期"),
    UNKNOWN_NOTIFICATION_TYPE(9105, "未知通知类型"),
    USER_CALLBACK_DATA_IS_EMPTY(9106, "回调数据为空 或 X-PD-SIGN 为空"),
    FAILED_TO_INSERT_A_CONSUMPTION_RECORD(9107, "插入消费记录失败"),

    PARAM_MISS(9400, "参数缺失"),
    PARAM_ERR(9401, "参数错误"),
    PHONE_ADDRESS(9402, "请填写 姓名/电话/地址"),

    FEE_ERR(9500, "费率设置错误"),

    /**
     * 代付10001
     */
    PAYMENT_PAY_CONFIG_PARAM(10001, "请遵循ISO-3166规范的2字符地区代码"),
    PAYMENT_PAY_ENTERPRISE(10002, "收款人类型为企业，公司名称不能为空"),
    PAYMENT_PAY_PAYEE_ID(10003, "请填写收款人PayeeId"),
    DOCUMENT_ERROR(10004, "收款人识别号，11位或14位数字"),
    ORDER_NOT_NOT_EXIST(10005, "订单不存在"),
    ORDER_IS_PROCESSING(10006, "订单正在处理中"),
    ORDER_IS_ERROR(10007, "订单支付失败"),
    ORDER_IS_TIME_OUT(10007, "订单超时"),
    /**
     * 汇率19001
     */
    SERVICE_CHARGE_IS_TOO_SMALL(19001, "手续费小于平台手续费"),
    RATE_IS_TOO_SMALL(19002, "费率小于平台费率"),
    PAYMENT_USER_CONFIG_NOT_EXIST(19003, "汇率配置不存在或被禁用"),
    ;


    private final long code;
    private final String message;

    private ResultCode(long code, String message) {
        this.code = code;
        this.message = message;
    }


    public String getMessage() {
        MessageSource messageSource = SpringContextUtil.getBean(MessageSource.class);
        String i8nMessage;
        try {
            i8nMessage = messageSource.getMessage("Code." + this.name(), null, LocaleContextHolder.getLocale());
        } catch (Exception e) {
            i8nMessage = this.message;
        }
        return i8nMessage;
    }


}
