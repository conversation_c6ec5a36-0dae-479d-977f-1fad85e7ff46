package info.znet.zcloud.validator;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @updateTime 2024/6/26 14:31
 */
@Component
public class InEnumValidator implements ConstraintValidator<InEnum, Object> {

    @Autowired
    MessageSource messageSource;

    private List<Object> listValue;
    private InEnum constraintAnnotation;

    @Override
    public void initialize(InEnum constraintAnnotation) {
        this.constraintAnnotation = constraintAnnotation;
        Class<? extends EnumValid> value = constraintAnnotation.value();
        EnumValid[] enumConstants = value.getEnumConstants();
        listValue = enumConstants[0].validValues();
    }


    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        // 如果值为 null，跳过 InEnum 的验证
        if (value == null) {
            return true;
        }
        if (!listValue.contains(value)) {
            String listStr = listValue.stream()
                    .map(Object::toString)
                    .collect(Collectors.joining(", "));
            context.disableDefaultConstraintViolation();
            String message = constraintAnnotation.message();
            if ("{Valid.InEnum}".equals(message)) {
                // 未设置自定义 message 时，使用默认提示
                message = messageSource.getMessage("Valid.InEnum", new Object[]{listStr}, LocaleContextHolder.getLocale());
            }
            context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
            return false;
        }
        return true;
    }
}

